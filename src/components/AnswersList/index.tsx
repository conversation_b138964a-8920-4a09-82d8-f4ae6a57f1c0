import { useEffect, useState, useRef, use<PERSON><PERSON>back, UIEvent } from "react";
import { useSearchParams } from "react-router-dom";
import cn from "classnames";

import * as interfaces from "interfaces";
import { AnswerServiceInstance, createAnswerServiceInstance } from "services";
import {
  useClientContext,
  useUserContext,
  useFilterAndSearchContext,
  useApplyFilterAndSearch,
  useTranslationContext,
} from "hooks";
import { getClientOptions } from "scripts/clients/index";

// Helper function to calculate top categories
const calculateTopCategories = (answers: interfaces.AnswerInterface[], limit: number = 3): interfaces.CategoryStatInterface[] => {
  if (!answers || answers.length === 0) return [];

  // Count occurrences of each category
  const categoryCounts: Record<string, number> = {};
  answers.forEach(answer => {
    const category = answer.question.category;
    if (category) {
      categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    }
  });

  // Convert to array of category stats with percentages
  const totalAnswers = answers.length;
  const categoryStats = Object.entries(categoryCounts).map(([name, count]) => ({
    name,
    percentage: Math.round((count / totalAnswers) * 100)
  }));

  // Sort by percentage (descending) and take top N
  return categoryStats
    .sort((a, b) => b.percentage - a.percentage)
    .slice(0, limit);
};

import AnswerItem from "../AnswerThumbnail";
import SelectedAnswer from "../AnswerItem";
import Icons from "shared/Icons";

import classes from "./AnswersList.module.scss";
import QuestionInput from "../AI/QuestionInput";
import { useClientLandingContext } from "../../pages/ClientLanding/context";
import emptyThumbnailUrl from "../../emptyThumbnailUrl";

createAnswerServiceInstance();

export default function AnswersList(props: { isMobile?: boolean }) {
  const { isMobile } = props;
  const { client, answers, setAnswers, setTopCategories } = useClientContext();
  const { user, userIsSet, isSignedIn } = useUserContext();
  const { filter, search } = useFilterAndSearchContext();
  const { t } = useTranslationContext();
  const {
    setOriginalItems: setOriginalAnswers,
    mappedItems: filteredAnswers,
    updateMappedItems: updateMappedAnswers,
  } = useApplyFilterAndSearch<interfaces.AnswerInterface>({ filter, search });
  const [searchParams] = useSearchParams();
  const answerIdFromUrl =
    searchParams.get("answerId") ||
    searchParams.get("aId") ||
    searchParams.get("answervId");

  const { setSelectedAnswer, selectAnswer, selectedAnswer } =
    useClientLandingContext();
  const [horizontalScrollPosition, setHorizontalScrollPosition] = useState(0);
  const [totalHorizontalScrollWidth, setTotalHorizontalScrollWidth] =
    useState(2);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(0);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMorePages, setHasMorePages] = useState(true);
  const [allLoadedAnswers, setAllLoadedAnswers] = useState<interfaces.AnswerInterface[]>([]);

  const listRef = useRef<HTMLHeadingElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handleListScroll = useCallback(
    (direction: "left" | "right") => {
      if (direction === "left") {
        listRef.current?.scroll({ left: listRef.current.scrollLeft - 186 });
      } else if (direction === "right") {
        listRef.current?.scroll({ left: listRef.current.scrollLeft + 186 });
      }
    },
    [listRef]
  );

  // Function to load more answers
  const loadMoreAnswers = useCallback(async () => {
    if (isLoadingMore || !hasMorePages || !client.id || !user.id) return;

    setIsLoadingMore(true);
    const nextPage = currentPage + 1;

    try {
      const newAnswers = await AnswerServiceInstance?.getAnswers(client.id, user.id, nextPage);

      if (newAnswers && newAnswers.length > 0) {
        const filterDate = (a: interfaces.AnswerInterface) => !a.endDate || new Date() < new Date(a.endDate);
        const validNewAnswers = newAnswers.filter((a) => !a.isDraft).filter(filterDate);

        // Append new answers to existing ones
        const updatedAnswers = [...allLoadedAnswers, ...validNewAnswers];
        setAllLoadedAnswers(updatedAnswers);
        setAnswers(updatedAnswers);
        setOriginalAnswers(updatedAnswers);
        setCurrentPage(nextPage);
      } else {
        // No more answers available
        setHasMorePages(false);
      }
    } catch (error) {
      console.error('Error loading more answers:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, hasMorePages, client.id, user.id, currentPage, allLoadedAnswers, setAnswers, setOriginalAnswers]);

  const handleScroll = useCallback(
    (event: UIEvent<HTMLElement> | any) => {
      const target = event?.target;

      if (totalHorizontalScrollWidth === 2) {
        setTotalHorizontalScrollWidth(target?.scrollWidth);
      }

      setHorizontalScrollPosition(target?.scrollLeft);

      // Check if we've scrolled to the bottom (within 100px threshold) for vertical scrolling
      if (target) {
        const { scrollTop, scrollHeight, clientHeight } = target;
        const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;

        if (isNearBottom && !isLoadingMore && hasMorePages) {
          loadMoreAnswers();
        }
      }
    },
    [totalHorizontalScrollWidth, isLoadingMore, hasMorePages, loadMoreAnswers]
  );

  const handleSelectedAnswerUpdate = useCallback(
    (answer: interfaces.AnswerInterface) => {
      updateMappedAnswers(((cur: interfaces.AnswerInterface[]) => {
        return cur.map((a) => {
          if (a.id === answer.id) return { ...a, ...answer };

          return a;
        });
      }) as any);

      setSelectedAnswer(answer);
    },
    [updateMappedAnswers]
  );

  useEffect(() => {
    selectAnswer(answerIdFromUrl);
  }, [answerIdFromUrl, answers]);

  useEffect(() => {
    const listContainer = listRef.current;

    if (listContainer) listContainer.addEventListener("scroll", handleScroll);

    return () => {
      if (listContainer)
        listContainer.removeEventListener("scroll", handleScroll);
    };
  }, [handleScroll]);

  useEffect(() => {
    if (!client.id || !user.id || !userIsSet) return;

    // Reset pagination state when filters change
    setCurrentPage(0);
    setHasMorePages(true);
    setIsLoadingMore(false);

    const filterDate = (a: interfaces.AnswerInterface) => !a.endDate || new Date() < new Date(a.endDate);

    AnswerServiceInstance?.getAnswers(client.id, user.id, 0).then(
      (answers: interfaces.AnswerInterface[]) => {
        const preparedAnswers = answers
          .filter((a) => !a.isDraft)
          .filter(
            (a) =>
              (filter.length && filter.includes(a.question.category)) ||
              !filter.length
          )
          .filter((a) => a.imageUrl !== emptyThumbnailUrl)
          .filter(
            (a) =>
              (search.length &&
                (a.question.text.toLowerCase().includes(search.toLowerCase()) ||
                  a.question.text.toLowerCase() === search.toLowerCase())) ||
              !search.length
          ).filter(filterDate);

        // Filter answers for non-draft and valid date
        const validAnswers = answers.filter((a) => !a.isDraft).filter(filterDate);

        // Sort answers to ensure consistent ordering (pinned first, then by creation date)
        const sortedAnswers = validAnswers.sort((a, b) => {
          // First sort by isPinned (pinned items first)
          const aPinned = (a as any).isPinned || false;
          const bPinned = (b as any).isPinned || false;
          if (aPinned && !bPinned) return -1;
          if (!aPinned && bPinned) return 1;

          // Then sort by creation date (newest first)
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        });

        // Calculate top categories from all valid answers (not just filtered ones)
        const topCats = calculateTopCategories(sortedAnswers);
        setTopCategories(topCats);

        // Set all states
        setAllLoadedAnswers(sortedAnswers);
        setOriginalAnswers(sortedAnswers);
        setAnswers(sortedAnswers);
        setSelectedAnswer(
          preparedAnswers?.length
            ? preparedAnswers[0]
            : ({} as interfaces.AnswerInterface)
        );
      }
    );
  }, [search, filter, userIsSet, isSignedIn, setTopCategories]);

  function autoPlayNextVideo(answer: interfaces.AnswerInterface) {
    setSelectedAnswer(answer);

    // setVideoState(cur => ({
    //   ...cur,
    //   isFeedbackShown: false,
    //   ended: false,
    //   started: false,
    //   paused: false,
    //   seeked: true,
    //   seeking: true,
    //   isDonationBannerShown: false,
    //   isMetaInfoShown: true
    // }))

    setTimeout(() => {
      if (!videoRef?.current) return;

      videoRef.current.currentTime = 0;
      videoRef.current.volume = 1;
      videoRef.current.play();
    }, 500);
  }

  const clientOptions = getClientOptions(client);
  const customSubHeaderText =
    clientOptions?.customText?.videoThumbnailHeader || null;
  const customHeaderText = clientOptions?.customText?.answerListHeader || null;
  const clientName =
    client.id === '211' ? 'The City'                : // BridgePort
    client.id === "276" ? "South Bay"               : // South Bay
    client.id === "269" ? "The City"                : // Traverse City
    client.id === '209' ? 'City'                    : // White Salmon
    client.id === '223' ? 'Bay City'                : // Bay City
    client.id === '235' ? 'Cold Lake'               : // Cold Lake
    client.id === '241' ? 'Fountain Hills'          : // Fountain Hills
    client.id === '261' ? 'the City'                : // Suisun City
    client.id === '275' ? 'Redondo PD'              : // Redondo PD
    client.id === '281' ? 'the Town of Morrisville' : // Morrisville
    client.id === '283' ? 'City of Emporia'         : // Emporia
    client.id === '287' ? 'Roeland Park'            : // Roeland
    client.id === '288' ? 'Park City'               : // Park City
    client.id === '285' ? 'Grand Rapids'            : // Grand Rapids

    ( client?.firstName || 'Candidate' ).replace( / /g, '' );
  const apostrophe =
    clientName.charAt(clientName.length - 1) === "s" ? `'` : `'s`;
  const headerText =
    customHeaderText || `${clientName}${apostrophe} ${t("Answers")}`;

  const showAiQuestions = client?.aiQuestionsEnabled;

  return (
    <>
      <div id="new_question" className={`${!showAiQuestions ? 'hidden' : ''} sm:hidden md:block`}>
        <QuestionInput callback={setSelectedAnswer} />
      </div>
      <div className={classes.container}>
        <div className={classes.leftColumn}>
          <SelectedAnswer
            selectedAnswer={selectedAnswer}
            handleVideoUpdate={(a) => handleSelectedAnswerUpdate(a)}
            videoRef={videoRef}
            isMobile={isMobile}
          />

          {customSubHeaderText && (
            <h2 className={cn(classes.subheader, "videoListSubHeader")}>
              {customSubHeaderText}
            </h2>
          )}
        </div>
        <div
          className={cn(
            classes.AnswersList
          )}
        >
          <div
            className={cn(classes.videoThumbnails, "videoThumbnails")}
            ref={listRef}
          >
            {filteredAnswers
              .filter((answer) => answer.imageUrl !== emptyThumbnailUrl)
              .map((answer: interfaces.AnswerInterface) => (
                <AnswerItem
                  key={answer.id}
                  answer={answer}
                  handleClick={autoPlayNextVideo}
                />
              ))}
            {isLoadingMore && (
              <div style={{
                padding: '20px',
                textAlign: 'center',
                color: '#666',
                fontSize: '14px'
              }}>
                Loading more answers...
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
