.audioDeviceSelector {
  background: #ffffff;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  &.compact {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;

    .icon {
      color: #28a745;
      font-size: 14px;
    }

    .deviceLabel {
      font-size: 14px;
      font-weight: 500;
      color: #495057;
    }

    .defaultBadge {
      background: #e9ecef;
      color: #6c757d;
      font-size: 11px;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 10px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  &.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 24px;
    color: #6c757d;
    font-size: 14px;

    svg {
      color: #007bff;
      font-size: 16px;
    }
  }

  &.error {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;

    svg {
      color: #dc3545;
      font-size: 16px;
    }

    .errorContent {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex: 1;
    }

    .retryButton {
      background: #dc3545;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: #c82333;
      }
    }
  }

  &.permission {
    .permissionContent {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 8px 0;

      .icon {
        color: #ffc107;
        font-size: 24px;
        flex-shrink: 0;
      }

      .text {
        flex: 1;

        h4 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #212529;
        }

        p {
          margin: 0;
          font-size: 14px;
          color: #6c757d;
        }
      }

      .permissionButton {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
        flex-shrink: 0;

        &:hover {
          background: #0056b3;
        }
      }
    }
  }

  &.noDevices {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 24px;
    color: #6c757d;
    text-align: center;

    svg {
      color: #dc3545;
      font-size: 20px;
    }

    .noDevicesContent {
      display: flex;
      flex-direction: column;
      gap: 12px;
      flex: 1;
    }

    .refreshButton {
      background: #6c757d;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: #5a6268;
      }
    }
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;

  .title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #212529;

    svg {
      color: #28a745;
      font-size: 16px;
    }
  }

  .refreshButton {
    background: transparent;
    color: #007bff;
    border: 1px solid #007bff;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: #007bff;
      color: white;
    }
  }
}

.deviceList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.deviceItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  transition: all 0.2s;
  cursor: pointer;

  &:hover {
    border-color: #007bff;
    background: #f8f9fa;
  }

  &.selected {
    border-color: #007bff;
    background: #e7f3ff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
  }

  .deviceLabel {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    cursor: pointer;

    .radioInput {
      margin: 0;
      cursor: pointer;
    }

    .deviceInfo {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;

      .deviceName {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        color: #212529;

        .defaultBadge {
          background: #28a745;
          color: white;
          font-size: 10px;
          font-weight: 600;
          padding: 2px 6px;
          border-radius: 10px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }

      .selectedIcon {
        color: #28a745;
        font-size: 14px;
      }
    }
  }

  .deviceActions {
    display: flex;
    align-items: center;
    gap: 8px;

    .audioLevelWrapper {
      min-width: 120px;
    }
  }

  .testButton {
    background: transparent;
    color: #6c757d;
    border: 1px solid #dee2e6;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    flex-shrink: 0;

    &:hover {
      color: #007bff;
      border-color: #007bff;
    }

    &.testing {
      color: #dc3545;
      border-color: #dc3545;
      background: rgba(220, 53, 69, 0.1);

      &:hover {
        background: rgba(220, 53, 69, 0.2);
      }
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    svg {
      font-size: 12px;
    }
  }
}

.footer {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;

  .hint {
    color: #6c757d;
    font-size: 12px;
    font-style: italic;
  }
}