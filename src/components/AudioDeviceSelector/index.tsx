import React, { useState, useRef, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faMicrophone,
  faMicrophoneSlash,
  faSpinner,
  faExclamationTriangle,
  faCheck,
  faPlay,
  faStop,
  faVolumeUp
} from '@fortawesome/free-solid-svg-icons';
import { useAudioDevices, AudioDevice } from 'hooks/useAudioDevices';
import { useServiceContext } from 'services/ServiceProvider';
import AudioLevelIndicator from 'components/AudioLevelIndicator';
import classes from './AudioDeviceSelector.module.scss';

interface AudioDeviceSelectorProps {
  onDeviceSelect?: (device: AudioDevice | null) => void;
  showTestButton?: boolean;
  showAudioLevel?: boolean;
  className?: string;
  compact?: boolean;
}

const AudioDeviceSelector: React.FC<AudioDeviceSelectorProps> = ({
  onDeviceSelect,
  showTestButton = true,
  showAudioLevel = true,
  className = '',
  compact = false
}) => {
  const { adminStatsService } = useServiceContext();
  const [testingDeviceId, setTestingDeviceId] = useState<string | null>(null);
  const [testStream, setTestStream] = useState<MediaStream | null>(null);
  const testStreamRef = useRef<MediaStream | null>(null);
  
  const {
    devices,
    selectedDeviceId,
    isLoading,
    error,
    hasPermission,
    isSupported,
    selectDevice,
    refreshDevices,
    requestPermission,
    testDevice,
    getSelectedDevice
  } = useAudioDevices();

  const handleDeviceSelect = (deviceId: string) => {
    selectDevice(deviceId);
    const selectedDevice = devices.find(d => d.deviceId === deviceId) || null;
    onDeviceSelect?.(selectedDevice);
    
    // Track device selection
    adminStatsService?.trackEvent('AudioDeviceSelector', 'device_selected', {
      eventLabel: selectedDevice?.label || 'Unknown Device'
    });
  };

  const handleTestDevice = async (deviceId: string) => {
    setTestingDeviceId(deviceId);

    try {
      // Clean up any existing test stream
      if (testStreamRef.current) {
        testStreamRef.current.getTracks().forEach(track => track.stop());
        testStreamRef.current = null;
        setTestStream(null);
      }

      // Create test stream for audio level monitoring
      const constraints: MediaStreamConstraints = {
        audio: deviceId === 'default' ? true : { deviceId: { exact: deviceId } }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      testStreamRef.current = stream;
      setTestStream(stream);

      const device = devices.find(d => d.deviceId === deviceId);

      adminStatsService?.trackEvent('AudioDeviceSelector', 'device_tested', {
        eventLabel: device?.label || 'Unknown Device',
        actionDetails: 'success'
      });

      // Keep test stream active for 5 seconds to show audio levels
      setTimeout(() => {
        if (testStreamRef.current) {
          testStreamRef.current.getTracks().forEach(track => track.stop());
          testStreamRef.current = null;
          setTestStream(null);
        }
        setTestingDeviceId(null);
      }, 5000);

    } catch (err) {
      console.error('Device test failed:', err);
      const device = devices.find(d => d.deviceId === deviceId);

      adminStatsService?.trackEvent('AudioDeviceSelector', 'device_tested', {
        eventLabel: device?.label || 'Unknown Device',
        actionDetails: 'failed'
      });

      setTestingDeviceId(null);
    }
  };

  const stopDeviceTest = () => {
    if (testStreamRef.current) {
      testStreamRef.current.getTracks().forEach(track => track.stop());
      testStreamRef.current = null;
      setTestStream(null);
    }
    setTestingDeviceId(null);
  };

  // Cleanup test streams on unmount
  useEffect(() => {
    return () => {
      if (testStreamRef.current) {
        testStreamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const handleRequestPermission = async () => {
    const granted = await requestPermission();
    if (granted) {
      adminStatsService?.trackEvent('AudioDeviceSelector', 'permission_granted');
    } else {
      adminStatsService?.trackEvent('AudioDeviceSelector', 'permission_denied');
    }
  };

  const handleRefreshDevices = () => {
    refreshDevices();
    adminStatsService?.trackEvent('AudioDeviceSelector', 'devices_refreshed');
  };

  // Not supported
  if (!isSupported) {
    return (
      <div className={`${classes.audioDeviceSelector} ${classes.error} ${className}`}>
        <FontAwesomeIcon icon={faExclamationTriangle} />
        <span>Audio device selection is not supported in this browser</span>
      </div>
    );
  }

  // No permission
  if (!hasPermission) {
    return (
      <div className={`${classes.audioDeviceSelector} ${classes.permission} ${className}`}>
        <div className={classes.permissionContent}>
          <FontAwesomeIcon icon={faMicrophoneSlash} className={classes.icon} />
          <div className={classes.text}>
            <h4>Microphone Access Required</h4>
            <p>Please allow microphone access to select audio devices</p>
          </div>
          <button 
            onClick={handleRequestPermission}
            className={classes.permissionButton}
          >
            Grant Permission
          </button>
        </div>
      </div>
    );
  }

  // Loading
  if (isLoading) {
    return (
      <div className={`${classes.audioDeviceSelector} ${classes.loading} ${className}`}>
        <FontAwesomeIcon icon={faSpinner} spin />
        <span>Detecting audio devices...</span>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`${classes.audioDeviceSelector} ${classes.error} ${className}`}>
        <FontAwesomeIcon icon={faExclamationTriangle} />
        <div className={classes.errorContent}>
          <span>{error}</span>
          <button onClick={handleRefreshDevices} className={classes.retryButton}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // No devices found
  if (devices.length === 0) {
    return (
      <div className={`${classes.audioDeviceSelector} ${classes.noDevices} ${className}`}>
        <FontAwesomeIcon icon={faMicrophoneSlash} />
        <div className={classes.noDevicesContent}>
          <span>No audio input devices found</span>
          <button onClick={handleRefreshDevices} className={classes.refreshButton}>
            Refresh
          </button>
        </div>
      </div>
    );
  }

  // Compact view for single device
  if (compact && devices.length === 1) {
    const device = devices[0];
    return (
      <div className={`${classes.audioDeviceSelector} ${classes.compact} ${className}`}>
        <FontAwesomeIcon icon={faMicrophone} className={classes.icon} />
        <span className={classes.deviceLabel}>{device.label}</span>
        {device.isDefault && <span className={classes.defaultBadge}>Default</span>}
      </div>
    );
  }

  // Full device selector
  return (
    <div className={`${classes.audioDeviceSelector} ${className}`}>
      <div className={classes.header}>
        <h4 className={classes.title}>
          <FontAwesomeIcon icon={faMicrophone} />
          Audio Input Device
        </h4>
        <button 
          onClick={handleRefreshDevices}
          className={classes.refreshButton}
          title="Refresh devices"
        >
          Refresh
        </button>
      </div>

      <div className={classes.deviceList}>
        {devices.map((device) => (
          <div
            key={device.deviceId}
            className={`${classes.deviceItem} ${
              selectedDeviceId === device.deviceId ? classes.selected : ''
            }`}
          >
            <label className={classes.deviceLabel}>
              <input
                type="radio"
                name="audioDevice"
                value={device.deviceId}
                checked={selectedDeviceId === device.deviceId}
                onChange={() => handleDeviceSelect(device.deviceId)}
                className={classes.radioInput}
              />
              <div className={classes.deviceInfo}>
                <div className={classes.deviceName}>
                  {device.label}
                  {device.isDefault && (
                    <span className={classes.defaultBadge}>Default</span>
                  )}
                </div>
                {selectedDeviceId === device.deviceId && (
                  <FontAwesomeIcon icon={faCheck} className={classes.selectedIcon} />
                )}
              </div>
            </label>
            
            <div className={classes.deviceActions}>
              {/* Audio Level Indicator for currently testing device */}
              {showAudioLevel && testingDeviceId === device.deviceId && testStream && (
                <div className={classes.audioLevelWrapper}>
                  <AudioLevelIndicator
                    stream={testStream}
                    deviceId={device.deviceId}
                    size="small"
                    showIcon={false}
                  />
                </div>
              )}

              {showTestButton && (
                <button
                  onClick={() => {
                    if (testingDeviceId === device.deviceId) {
                      stopDeviceTest();
                    } else {
                      handleTestDevice(device.deviceId);
                    }
                  }}
                  className={`${classes.testButton} ${
                    testingDeviceId === device.deviceId ? classes.testing : ''
                  }`}
                  title={testingDeviceId === device.deviceId ? "Stop test" : "Test this device"}
                >
                  {testingDeviceId === device.deviceId ? (
                    <FontAwesomeIcon icon={faStop} />
                  ) : (
                    <FontAwesomeIcon icon={faVolumeUp} />
                  )}
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {devices.length > 1 && (
        <div className={classes.footer}>
          <small className={classes.hint}>
            Select your preferred microphone for recording
          </small>
        </div>
      )}
    </div>
  );
};

export default AudioDeviceSelector;
