.audioLevelIndicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  &.small {
    gap: 8px;
    padding: 6px 8px;
    
    .icon {
      font-size: 12px;
    }
    
    .levelBars {
      height: 12px;
      gap: 2px;
    }
    
    .levelBar {
      width: 3px;
    }
    
    .levelText {
      font-size: 10px;
    }
  }

  &.medium {
    gap: 12px;
    padding: 8px 12px;
    
    .icon {
      font-size: 14px;
    }
    
    .levelBars {
      height: 16px;
      gap: 3px;
    }
    
    .levelBar {
      width: 4px;
    }
    
    .levelText {
      font-size: 12px;
    }
  }

  &.large {
    gap: 16px;
    padding: 12px 16px;
    
    .icon {
      font-size: 18px;
    }
    
    .levelBars {
      height: 20px;
      gap: 4px;
    }
    
    .levelBar {
      width: 5px;
    }
    
    .levelText {
      font-size: 14px;
    }
  }
}

.icon {
  flex-shrink: 0;
  transition: color 0.2s ease;

  &.active {
    color: #28a745;
  }

  &.inactive {
    color: #6c757d;
  }
}

.levelMeter {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.levelBars {
  display: flex;
  align-items: end;
  gap: 3px;
  height: 16px;
}

.levelBar {
  width: 4px;
  border-radius: 2px;
  background: #e9ecef;
  transition: all 0.1s ease;
  
  &:nth-child(1) { height: 20%; }
  &:nth-child(2) { height: 40%; }
  &:nth-child(3) { height: 60%; }
  &:nth-child(4) { height: 80%; }
  &:nth-child(5) { height: 100%; }

  &.active {
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
  }
}

.levelText {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  min-width: 60px;
  text-align: right;
}
