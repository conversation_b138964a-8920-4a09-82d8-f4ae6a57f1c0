import React, { useEffect, useRef, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMicrophone, faMicrophoneSlash } from '@fortawesome/free-solid-svg-icons';
import classes from './AudioLevelIndicator.module.scss';

interface AudioLevelIndicatorProps {
  stream?: MediaStream | null;
  deviceId?: string;
  className?: string;
  showIcon?: boolean;
  size?: 'small' | 'medium' | 'large';
}

const AudioLevelIndicator: React.FC<AudioLevelIndicatorProps> = ({
  stream,
  deviceId,
  className = '',
  showIcon = true,
  size = 'medium'
}) => {
  const [audioLevel, setAudioLevel] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);

  useEffect(() => {
    if (!stream) {
      setAudioLevel(0);
      setIsActive(false);
      return;
    }

    const audioTracks = stream.getAudioTracks();
    if (audioTracks.length === 0) {
      setAudioLevel(0);
      setIsActive(false);
      return;
    }

    try {
      // Create audio context and analyser
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const analyser = audioContext.createAnalyser();
      const source = audioContext.createMediaStreamSource(stream);
      
      analyser.fftSize = 256;
      analyser.smoothingTimeConstant = 0.8;
      source.connect(analyser);
      
      audioContextRef.current = audioContext;
      analyserRef.current = analyser;
      setIsActive(true);

      // Start monitoring audio levels
      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      
      const updateAudioLevel = () => {
        if (!analyserRef.current) return;
        
        analyserRef.current.getByteFrequencyData(dataArray);
        
        // Calculate RMS (Root Mean Square) for more accurate level detection
        let sum = 0;
        for (let i = 0; i < dataArray.length; i++) {
          sum += dataArray[i] * dataArray[i];
        }
        const rms = Math.sqrt(sum / dataArray.length);
        
        // Normalize to 0-100 range
        const level = Math.min(100, (rms / 128) * 100);
        setAudioLevel(level);
        
        animationFrameRef.current = requestAnimationFrame(updateAudioLevel);
      };
      
      updateAudioLevel();
      
    } catch (error) {
      console.error('Failed to create audio level monitor:', error);
      setIsActive(false);
    }

    return () => {
      // Cleanup
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
      }
      
      setAudioLevel(0);
      setIsActive(false);
    };
  }, [stream, deviceId]);

  const getLevelColor = (level: number): string => {
    if (level < 20) return '#28a745'; // Green - good level
    if (level < 70) return '#ffc107'; // Yellow - moderate level
    return '#dc3545'; // Red - high level
  };

  const getLevelBars = (): number => {
    return Math.ceil((audioLevel / 100) * 5); // 5 bars maximum
  };

  return (
    <div className={`${classes.audioLevelIndicator} ${classes[size]} ${className}`}>
      {showIcon && (
        <FontAwesomeIcon 
          icon={isActive ? faMicrophone : faMicrophoneSlash} 
          className={`${classes.icon} ${isActive ? classes.active : classes.inactive}`}
        />
      )}
      
      <div className={classes.levelMeter}>
        <div className={classes.levelBars}>
          {[1, 2, 3, 4, 5].map((bar) => (
            <div
              key={bar}
              className={`${classes.levelBar} ${
                bar <= getLevelBars() ? classes.active : ''
              }`}
              style={{
                backgroundColor: bar <= getLevelBars() ? getLevelColor(audioLevel) : '#e9ecef'
              }}
            />
          ))}
        </div>
        
        <div className={classes.levelText}>
          {isActive ? `${Math.round(audioLevel)}%` : 'No Signal'}
        </div>
      </div>
    </div>
  );
};

export default AudioLevelIndicator;
