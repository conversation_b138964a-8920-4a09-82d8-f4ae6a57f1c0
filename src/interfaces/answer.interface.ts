import { QuestionInterface } from './question.interface';

import { supportedLanguages } from 'hooks/useTranslationContext.hook';

interface VideoUrlsInterface {
  mp4: string;
  ogv: string;
  webm: string;
}

type AwsTranscriptionAudioSegment = {
  start_time: string;
  end_time: string;
  transcript: string;
};

interface AwsTranscription {
  items: any[];
  transcripts: any[];
  audio_segments: AwsTranscriptionAudioSegment[];
}

export interface AnswerInterface {
  id: string;
  clientId: number;

  imageUrl: string;
  videoUrl: string;
  videoUrls?: VideoUrlsInterface;

  likes: number;
  votes: number;
  question: QuestionInterface;

  videoDuration: number | null;

  subtitles: string | null;
  subtitlesSpeed: number | null;
  subtitlesTranslations: Record<typeof supportedLanguages[number], string> | null;

  transcription: AwsTranscription | null;
  transcriptionTranslation: Record<typeof supportedLanguages[number], AwsTranscriptionAudioSegment[]> | null;
  showTranscribedSubtitles: boolean;

  createdAtDateString: string;
  userVotes: any[][];
  userLikes: any[][];
  sends: any;

  liked: boolean;
  enabled: boolean;
  createdAt: string | Date;
  updatedAt: string | Date;
  isDraft: boolean;
  isPinned: boolean;

  endDate: string | Date;
}

export interface LikeInterface {
  id: string;

  userId: string;
  answerId: string;
  amount: number;

  enabled: true;
  createdAt: string | Date;
  updatedAt: string | Date;
}
