import { useCallback, useEffect, useRef } from 'react';
import { useAudioDeviceContext } from 'contexts/AudioDeviceContext';
import logger from 'utils/logger';

export interface AudioDeviceErrorHandler {
  handleDeviceError: (error: Error, deviceId?: string) => Promise<MediaStream | null>;
  handlePermissionError: (error: Error) => Promise<boolean>;
  handleDeviceDisconnection: (deviceId: string) => Promise<void>;
  isRecoverable: (error: Error) => boolean;
}

export const useAudioDeviceErrorHandler = (): AudioDeviceErrorHandler => {
  const audioDeviceContext = useAudioDeviceContext();
  const fallbackAttempts = useRef<number>(0);
  const maxFallbackAttempts = 3;

  // Reset fallback attempts when device changes successfully
  useEffect(() => {
    if (audioDeviceContext.selectedDevice && !audioDeviceContext.error) {
      fallbackAttempts.current = 0;
    }
  }, [audioDeviceContext.selectedDevice, audioDeviceContext.error]);

  // Check if an error is recoverable
  const isRecoverable = useCallback((error: Error): boolean => {
    const errorMessage = error.message.toLowerCase();
    
    // Recoverable errors
    const recoverableErrors = [
      'requested device not found',
      'devicenotfounderror',
      'constraint not satisfied',
      'overconstrained',
      'device in use',
      'device busy',
      'device disconnected'
    ];

    return recoverableErrors.some(recoverableError => 
      errorMessage.includes(recoverableError)
    );
  }, []);

  // Handle device-specific errors with fallback strategies
  const handleDeviceError = useCallback(async (
    error: Error, 
    deviceId?: string
  ): Promise<MediaStream | null> => {
    logger.error(`Audio device error for device ${deviceId}:`, error);

    // Prevent infinite fallback loops
    if (fallbackAttempts.current >= maxFallbackAttempts) {
      logger.error('Maximum fallback attempts reached, giving up');
      return null;
    }

    fallbackAttempts.current++;

    // If the error is not recoverable, don't attempt fallback
    if (!isRecoverable(error)) {
      logger.error('Non-recoverable audio device error:', error);
      return null;
    }

    try {
      // Strategy 1: Try default device if specific device failed
      if (deviceId && deviceId !== 'default') {
        logger.info('Attempting fallback to default audio device');
        
        const defaultDevice = audioDeviceContext.devices.find(d => d.isDefault);
        if (defaultDevice) {
          audioDeviceContext.selectDevice(defaultDevice.deviceId);
          
          const fallbackStream = await navigator.mediaDevices.getUserMedia({
            audio: true,
            video: true
          });
          
          logger.info('Successfully fell back to default audio device');
          return fallbackStream;
        }
      }

      // Strategy 2: Try any available device
      if (audioDeviceContext.devices.length > 1) {
        const alternativeDevice = audioDeviceContext.devices.find(d => 
          d.deviceId !== deviceId && d.deviceId !== audioDeviceContext.selectedDeviceId
        );

        if (alternativeDevice) {
          logger.info(`Attempting fallback to alternative device: ${alternativeDevice.label}`);
          
          audioDeviceContext.selectDevice(alternativeDevice.deviceId);
          
          const alternativeStream = await navigator.mediaDevices.getUserMedia({
            audio: { deviceId: { exact: alternativeDevice.deviceId } },
            video: true
          });
          
          logger.info(`Successfully fell back to alternative device: ${alternativeDevice.label}`);
          return alternativeStream;
        }
      }

      // Strategy 3: Use basic audio constraints without device specification
      logger.info('Attempting fallback with basic audio constraints');
      
      const basicStream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: true
      });
      
      logger.info('Successfully fell back to basic audio constraints');
      return basicStream;

    } catch (fallbackError) {
      logger.error('All fallback strategies failed:', fallbackError);
      
      // Refresh device list in case devices changed
      try {
        await audioDeviceContext.refreshDevices();
      } catch (refreshError) {
        logger.error('Failed to refresh devices after error:', refreshError);
      }
      
      return null;
    }
  }, [audioDeviceContext, isRecoverable]);

  // Handle permission-related errors
  const handlePermissionError = useCallback(async (error: Error): Promise<boolean> => {
    logger.error('Audio permission error:', error);

    const errorMessage = error.message.toLowerCase();
    
    // Check if it's a permission error
    const isPermissionError = [
      'permission denied',
      'notallowederror',
      'permissiondeniederror'
    ].some(permError => errorMessage.includes(permError));

    if (!isPermissionError) {
      return false;
    }

    // Try to request permission again
    try {
      const granted = await audioDeviceContext.requestPermission();
      if (granted) {
        logger.info('Permission granted after retry');
        return true;
      }
    } catch (retryError) {
      logger.error('Permission retry failed:', retryError);
    }

    return false;
  }, [audioDeviceContext]);

  // Handle device disconnection during recording
  const handleDeviceDisconnection = useCallback(async (deviceId: string): Promise<void> => {
    logger.warn(`Audio device disconnected: ${deviceId}`);

    try {
      // Refresh device list to get current state
      await audioDeviceContext.refreshDevices();

      // Check if the disconnected device is currently selected
      if (audioDeviceContext.selectedDeviceId === deviceId) {
        // Find a replacement device
        const availableDevices = audioDeviceContext.devices.filter(d => d.deviceId !== deviceId);
        
        if (availableDevices.length > 0) {
          const replacementDevice = availableDevices.find(d => d.isDefault) || availableDevices[0];
          
          logger.info(`Switching to replacement device: ${replacementDevice.label}`);
          audioDeviceContext.selectDevice(replacementDevice.deviceId);
        } else {
          logger.warn('No replacement audio devices available');
        }
      }
    } catch (error) {
      logger.error('Failed to handle device disconnection:', error);
    }
  }, [audioDeviceContext]);

  // Listen for device changes and handle disconnections
  useEffect(() => {
    const handleDeviceChange = async () => {
      try {
        const currentDevices = await navigator.mediaDevices.enumerateDevices();
        const currentAudioDevices = currentDevices.filter(d => d.kind === 'audioinput');
        
        // Check if selected device is still available
        if (audioDeviceContext.selectedDeviceId) {
          const selectedDeviceStillExists = currentAudioDevices.some(
            d => d.deviceId === audioDeviceContext.selectedDeviceId
          );
          
          if (!selectedDeviceStillExists) {
            await handleDeviceDisconnection(audioDeviceContext.selectedDeviceId);
          }
        }
      } catch (error) {
        logger.error('Error checking device changes:', error);
      }
    };

    if (audioDeviceContext.isSupported) {
      navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);
      
      return () => {
        navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
      };
    }
  }, [audioDeviceContext, handleDeviceDisconnection]);

  return {
    handleDeviceError,
    handlePermissionError,
    handleDeviceDisconnection,
    isRecoverable
  };
};
