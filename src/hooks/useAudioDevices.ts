import { useState, useEffect, useCallback, useRef } from 'react';
import logger from 'utils/logger';

export interface AudioDevice {
  deviceId: string;
  label: string;
  groupId: string;
  kind: 'audioinput';
  isDefault: boolean;
}

export interface AudioDeviceState {
  devices: AudioDevice[];
  selectedDeviceId: string | null;
  isLoading: boolean;
  error: string | null;
  hasPermission: boolean;
  isSupported: boolean;
}

export interface AudioDeviceActions {
  selectDevice: (deviceId: string) => void;
  refreshDevices: () => Promise<void>;
  requestPermission: () => Promise<boolean>;
  testDevice: (deviceId: string) => Promise<boolean>;
  getSelectedDevice: () => AudioDevice | null;
}

const DEFAULT_DEVICE_ID = 'default';

export const useAudioDevices = (): AudioDeviceState & AudioDeviceActions => {
  const [devices, setDevices] = useState<AudioDevice[]>([]);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const testStreamRef = useRef<MediaStream | null>(null);

  // Check if MediaDevices API is supported
  useEffect(() => {
    const supported = !!(
      navigator.mediaDevices && 
      navigator.mediaDevices.enumerateDevices &&
      navigator.mediaDevices.getUserMedia
    );
    setIsSupported(supported);
    
    if (!supported) {
      setError('Audio device enumeration is not supported in this browser');
      setIsLoading(false);
      logger.warn('MediaDevices API not supported');
    }
  }, []);

  // Request microphone permission
  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!isSupported) return false;

    try {
      setError(null);
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Stop the stream immediately as we only needed permission
      stream.getTracks().forEach(track => track.stop());
      
      setHasPermission(true);
      logger.info('Audio permission granted');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Permission denied';
      setError(`Microphone permission denied: ${errorMessage}`);
      setHasPermission(false);
      logger.error('Audio permission denied:', err);
      return false;
    }
  }, [isSupported]);

  // Enumerate available audio input devices
  const enumerateDevices = useCallback(async (): Promise<AudioDevice[]> => {
    if (!isSupported || !hasPermission) return [];

    try {
      const mediaDevices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = mediaDevices.filter(device => device.kind === 'audioinput');
      
      const audioDevices: AudioDevice[] = audioInputs.map((device, index) => ({
        deviceId: device.deviceId || `device-${index}`,
        label: device.label || `Microphone ${index + 1}`,
        groupId: device.groupId || '',
        kind: 'audioinput' as const,
        isDefault: device.deviceId === 'default' || index === 0
      }));

      // Ensure we always have a default device
      if (audioDevices.length > 0 && !audioDevices.some(d => d.isDefault)) {
        audioDevices[0].isDefault = true;
      }

      logger.info(`Found ${audioDevices.length} audio input devices`);
      return audioDevices;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to enumerate devices';
      logger.error('Failed to enumerate audio devices:', err);
      throw new Error(errorMessage);
    }
  }, [isSupported, hasPermission]);

  // Refresh device list
  const refreshDevices = useCallback(async (): Promise<void> => {
    if (!isSupported) return;

    setIsLoading(true);
    setError(null);

    try {
      // Request permission if we don't have it
      if (!hasPermission) {
        const permissionGranted = await requestPermission();
        if (!permissionGranted) {
          setIsLoading(false);
          return;
        }
      }

      const audioDevices = await enumerateDevices();
      setDevices(audioDevices);

      // Auto-select default device if none selected or selected device no longer exists
      if (!selectedDeviceId || !audioDevices.find(d => d.deviceId === selectedDeviceId)) {
        const defaultDevice = audioDevices.find(d => d.isDefault) || audioDevices[0];
        if (defaultDevice) {
          setSelectedDeviceId(defaultDevice.deviceId);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh devices';
      setError(errorMessage);
      logger.error('Failed to refresh audio devices:', err);
    } finally {
      setIsLoading(false);
    }
  }, [isSupported, hasPermission, selectedDeviceId, requestPermission, enumerateDevices]);

  // Test if a specific device is working
  const testDevice = useCallback(async (deviceId: string): Promise<boolean> => {
    if (!isSupported) return false;

    try {
      // Clean up any existing test stream
      if (testStreamRef.current) {
        testStreamRef.current.getTracks().forEach(track => track.stop());
        testStreamRef.current = null;
      }

      const constraints: MediaStreamConstraints = {
        audio: deviceId === DEFAULT_DEVICE_ID ? true : { deviceId: { exact: deviceId } }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      testStreamRef.current = stream;

      // Test for a brief moment to ensure device is working
      await new Promise(resolve => setTimeout(resolve, 100));

      // Clean up test stream
      stream.getTracks().forEach(track => track.stop());
      testStreamRef.current = null;

      logger.info(`Audio device test successful: ${deviceId}`);
      return true;
    } catch (err) {
      logger.error(`Audio device test failed for ${deviceId}:`, err);
      return false;
    }
  }, [isSupported]);

  // Select a specific device
  const selectDevice = useCallback((deviceId: string) => {
    const device = devices.find(d => d.deviceId === deviceId);
    if (device) {
      setSelectedDeviceId(deviceId);
      logger.info(`Selected audio device: ${device.label} (${deviceId})`);
    } else {
      logger.warn(`Attempted to select unknown device: ${deviceId}`);
    }
  }, [devices]);

  // Get currently selected device
  const getSelectedDevice = useCallback((): AudioDevice | null => {
    return devices.find(d => d.deviceId === selectedDeviceId) || null;
  }, [devices, selectedDeviceId]);

  // Initialize on mount and listen for device changes
  useEffect(() => {
    if (isSupported) {
      refreshDevices();

      // Listen for device changes
      const handleDeviceChange = () => {
        logger.info('Audio devices changed, refreshing...');
        refreshDevices();
      };

      navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);

      return () => {
        navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
        
        // Clean up any test streams
        if (testStreamRef.current) {
          testStreamRef.current.getTracks().forEach(track => track.stop());
          testStreamRef.current = null;
        }
      };
    }
  }, [isSupported, refreshDevices]);

  return {
    // State
    devices,
    selectedDeviceId,
    isLoading,
    error,
    hasPermission,
    isSupported,
    
    // Actions
    selectDevice,
    refreshDevices,
    requestPermission,
    testDevice,
    getSelectedDevice
  };
};
