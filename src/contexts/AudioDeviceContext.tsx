import React, { createContext, useContext, useCallback, useEffect, useState } from 'react';
import { useAudioDevices, AudioDevice } from 'hooks/useAudioDevices';
import logger from 'utils/logger';

interface AudioDeviceContextType {
  // Device state
  devices: AudioDevice[];
  selectedDevice: AudioDevice | null;
  selectedDeviceId: string | null;
  isLoading: boolean;
  error: string | null;
  hasPermission: boolean;
  isSupported: boolean;

  // Device actions
  selectDevice: (deviceId: string) => void;
  refreshDevices: () => Promise<void>;
  requestPermission: () => Promise<boolean>;
  testDevice: (deviceId: string) => Promise<boolean>;

  // Media stream management
  getMediaConstraints: () => MediaStreamConstraints;
  createAudioStream: () => Promise<MediaStream>;
  
  // Preferences
  saveDevicePreference: (deviceId: string) => void;
  loadDevicePreference: () => string | null;
  clearDevicePreference: () => void;
}

const AudioDeviceContext = createContext<AudioDeviceContextType | null>(null);

const DEVICE_PREFERENCE_KEY = 'repd_admin_audio_device_preference';

interface AudioDeviceProviderProps {
  children: React.ReactNode;
}

export const AudioDeviceProvider: React.FC<AudioDeviceProviderProps> = ({ children }) => {
  const audioDevices = useAudioDevices();
  const [currentStream, setCurrentStream] = useState<MediaStream | null>(null);

  const {
    devices,
    selectedDeviceId,
    isLoading,
    error,
    hasPermission,
    isSupported,
    selectDevice: selectDeviceHook,
    refreshDevices,
    requestPermission,
    testDevice,
    getSelectedDevice
  } = audioDevices;

  // Get currently selected device
  const selectedDevice = getSelectedDevice();

  // Save device preference to localStorage
  const saveDevicePreference = useCallback((deviceId: string) => {
    try {
      localStorage.setItem(DEVICE_PREFERENCE_KEY, deviceId);
      logger.info(`Saved audio device preference: ${deviceId}`);
    } catch (err) {
      logger.warn('Failed to save audio device preference:', err);
    }
  }, []);

  // Load device preference from localStorage
  const loadDevicePreference = useCallback((): string | null => {
    try {
      const savedDeviceId = localStorage.getItem(DEVICE_PREFERENCE_KEY);
      if (savedDeviceId) {
        logger.info(`Loaded audio device preference: ${savedDeviceId}`);
        return savedDeviceId;
      }
    } catch (err) {
      logger.warn('Failed to load audio device preference:', err);
    }
    return null;
  }, []);

  // Clear device preference
  const clearDevicePreference = useCallback(() => {
    try {
      localStorage.removeItem(DEVICE_PREFERENCE_KEY);
      logger.info('Cleared audio device preference');
    } catch (err) {
      logger.warn('Failed to clear audio device preference:', err);
    }
  }, []);

  // Enhanced device selection with preference saving
  const selectDevice = useCallback((deviceId: string) => {
    selectDeviceHook(deviceId);
    saveDevicePreference(deviceId);
  }, [selectDeviceHook, saveDevicePreference]);

  // Load saved preference on mount
  useEffect(() => {
    if (devices.length > 0 && !selectedDeviceId) {
      const savedDeviceId = loadDevicePreference();
      if (savedDeviceId) {
        const savedDevice = devices.find(d => d.deviceId === savedDeviceId);
        if (savedDevice) {
          selectDeviceHook(savedDeviceId);
          logger.info(`Restored saved audio device: ${savedDevice.label}`);
        } else {
          // Saved device no longer exists, clear preference
          clearDevicePreference();
          logger.info('Saved audio device no longer available, cleared preference');
        }
      }
    }
  }, [devices, selectedDeviceId, selectDeviceHook, loadDevicePreference, clearDevicePreference]);

  // Get media constraints for the selected device
  const getMediaConstraints = useCallback((): MediaStreamConstraints => {
    const audioConstraints = selectedDeviceId && selectedDeviceId !== 'default'
      ? { deviceId: { exact: selectedDeviceId } }
      : true;

    return {
      audio: audioConstraints,
      video: true // Keep video for existing video recording functionality
    };
  }, [selectedDeviceId]);

  // Create audio stream with selected device and comprehensive error handling
  const createAudioStream = useCallback(async (): Promise<MediaStream> => {
    // Clean up existing stream
    if (currentStream) {
      currentStream.getTracks().forEach(track => track.stop());
      setCurrentStream(null);
    }

    try {
      const constraints = getMediaConstraints();
      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      setCurrentStream(stream);
      logger.info(`Created audio stream with device: ${selectedDevice?.label || 'default'}`);

      return stream;
    } catch (err) {
      logger.error('Failed to create audio stream:', err);

      // Enhanced error handling with multiple fallback strategies
      const error = err as Error;
      const errorMessage = error.message.toLowerCase();

      // Strategy 1: Handle device-specific errors
      if (selectedDeviceId && selectedDeviceId !== 'default' && (
        errorMessage.includes('requested device not found') ||
        errorMessage.includes('devicenotfounderror') ||
        errorMessage.includes('constraint not satisfied')
      )) {
        logger.info('Selected device unavailable, attempting fallback to default');

        try {
          const fallbackConstraints: MediaStreamConstraints = {
            audio: true,
            video: true
          };

          const fallbackStream = await navigator.mediaDevices.getUserMedia(fallbackConstraints);
          setCurrentStream(fallbackStream);

          // Update selection to default device
          const defaultDevice = devices.find(d => d.isDefault);
          if (defaultDevice) {
            selectDeviceHook(defaultDevice.deviceId);
            logger.info(`Switched to default device: ${defaultDevice.label}`);
          }

          return fallbackStream;
        } catch (fallbackErr) {
          logger.error('Fallback to default device failed:', fallbackErr);
        }
      }

      // Strategy 2: Try alternative devices if available
      if (devices.length > 1) {
        const alternativeDevice = devices.find(d =>
          d.deviceId !== selectedDeviceId && d.deviceId !== 'default'
        );

        if (alternativeDevice) {
          logger.info(`Attempting fallback to alternative device: ${alternativeDevice.label}`);

          try {
            const altConstraints: MediaStreamConstraints = {
              audio: { deviceId: { exact: alternativeDevice.deviceId } },
              video: true
            };

            const altStream = await navigator.mediaDevices.getUserMedia(altConstraints);
            setCurrentStream(altStream);
            selectDeviceHook(alternativeDevice.deviceId);

            logger.info(`Successfully switched to alternative device: ${alternativeDevice.label}`);
            return altStream;
          } catch (altErr) {
            logger.error(`Alternative device ${alternativeDevice.label} also failed:`, altErr);
          }
        }
      }

      // Strategy 3: Basic audio without device constraints
      try {
        logger.info('Attempting basic audio stream without device constraints');
        const basicStream = await navigator.mediaDevices.getUserMedia({
          audio: true,
          video: true
        });

        setCurrentStream(basicStream);
        logger.info('Successfully created basic audio stream');
        return basicStream;
      } catch (basicErr) {
        logger.error('Basic audio stream creation failed:', basicErr);
      }

      // All strategies failed
      throw new Error(`Failed to create audio stream: ${error.message}. All fallback strategies exhausted.`);
    }
  }, [selectedDeviceId, selectedDevice, currentStream, getMediaConstraints, devices, selectDeviceHook]);

  // Clean up streams on unmount
  useEffect(() => {
    return () => {
      if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
      }
    };
  }, [currentStream]);

  const contextValue: AudioDeviceContextType = {
    // Device state
    devices,
    selectedDevice,
    selectedDeviceId,
    isLoading,
    error,
    hasPermission,
    isSupported,

    // Device actions
    selectDevice,
    refreshDevices,
    requestPermission,
    testDevice,

    // Media stream management
    getMediaConstraints,
    createAudioStream,

    // Preferences
    saveDevicePreference,
    loadDevicePreference,
    clearDevicePreference
  };

  return (
    <AudioDeviceContext.Provider value={contextValue}>
      {children}
    </AudioDeviceContext.Provider>
  );
};

export const useAudioDeviceContext = (): AudioDeviceContextType => {
  const context = useContext(AudioDeviceContext);
  if (!context) {
    throw new Error('useAudioDeviceContext must be used within an AudioDeviceProvider');
  }
  return context;
};
